"use client";
import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { ChevronLeft } from "lucide-react";
import { useDispatch } from "react-redux";
import { useAppSelector } from "@/app/store/store";
import { usePackageList } from "@/app/hooks/usePackageList";
import { useInfiniteScroll } from "@/app/hooks/useInfiniteScroll";
import { useFilterPackages } from "@/app/hooks/useFilterPackages";
import { initializePackage } from "@/app/store/features/packageSlice";
import ExploreFilter from "./_components/explore-filter/ExploreFilter";
import SwiperComponent from "./_components/explore-filter/Swiper";
import FilterCardList from "./_components/explore-filter/FilterCardList";
import PackagesLoading from "@/app/(user-area)/components/loading/PackagesLoading";
import SelectedData from "./_components/explore-filter/SelectedData";
import FilterPackages from "./_components/explore-filter/FilterPackages";
import Link from "next/link";
import { setPackageId } from "@/app/store/features/packageDetailsSlice";
import PackagesLoadingFull from "@/app/(user-area)/components/loading/PackagesLoadingFull";

const Page = () => {
  const router = useRouter();
  const dispatch = useDispatch();
  const isLoggedIn = useAppSelector((state) => state.authSlice.isLoggedIn);
  const [searchParams, setSearchParams] = useState(null);
  const { currentPackageId } = useAppSelector((state) => state.packageDetails);
  const [loading, setLoading] = useState(false);

  const [offset, setOffset] = useState<number>(0);
  const { packageList, isLoading, packageListHasNext, err } =
    usePackageList(offset);
  const filteredPackages = useFilterPackages(packageList);
  const destination = useAppSelector(
    (state) => state.searchPackage.destination
  );
  console.log(filteredPackages, "filterpackage");

  const fetchMoreItems = () => {
    if (packageListHasNext) {
      setOffset(offset + 10);
    }
  };

  const lastElementRef = useInfiniteScroll({
    hasMore: packageListHasNext,
    onLoadMore: fetchMoreItems,
  });

  const handleNextPage = (packageId: string) => {
    router.push(`/package/${packageId}`);
  };

  // useEffect(() => {
  //   if (currentPackageId) {
  //     setLoading(false); // Stop loading once ID is set
  //     router.push(`/package/${currentPackageId}`);
  //   }
  // }, [currentPackageId, router]);

  return loading ? (
    <PackagesLoadingFull />
  ) : (
    <>
      {/* <div className="hidden lg:flex justify-between z-10 w-full bg-white border-b-2  fixed top-0 py-7 ">
        <div className="px-10">
          <h1 className="text-2xl font-semibold text-[#FF5F5F]">Tripxplo</h1>
        </div>
        <div className="flex gap-20 px-20">
          <Link className="hover:text-[#FF5F5F]" href="/">
            Home
          </Link>
          <Link className="hover:text-[#FF5F5F]" href="/sign-in">
            Login
          </Link>
          <Link className="hover:text-[#FF5F5F] " href="/">
            Profile
          </Link>
          <Link className="hover:text-[#FF5F5F]  " href="/register">
            Register
          </Link>
        </div>
      </div> */}
      <div className="sm:px-2 lg:px-10 min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
        <div className="flex items-center pl-3 mt-6 gap-6">
          <div
            className="p-3 border border-gray-200 shadow-md rounded-xl flex justify-center cursor-pointer hover:shadow-lg hover:border-gray-300 transition-all duration-300 hover:scale-105 bg-white/80 backdrop-blur-sm"
            onClick={() => router.push("/")}
          >
            <ChevronLeft size={24} className="text-[#FF5F5F] hover:text-[#FF4444] transition-colors duration-300" />
          </div>

          <div className="flex flex-col justify-center">
            <header className="font-Poppins sm:text-[20px] lg:text-[28px] font-bold text-[#FF5F5F] drop-shadow-sm">
              Top Packages
            </header>
            <h1 className="text-gray-600 sm:text-xs lg:text-lg font-medium">
              Highly Rated Packages across Countries
            </h1>
          </div>
        </div>
        {/* Large Screen Layout */}
        <div className="hidden lg:block">
          {/* Full-width Swiper with Overlay Text */}
          <div className="relative w-full mt-8 rounded-2xl overflow-hidden shadow-xl">
            <SwiperComponent />
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 z-10">
              <div className="text-center text-white">
                <header className="text-[24px] font-semibold">
                  List of Packages
                </header>
                <h1 className="text-lg font-medium opacity-90">
                  Various Packages available for{" "}
                  <span className="font-medium">{destination}</span>
                </h1>
              </div>
            </div>
          </div>

          {/* Filters and Package Grid */}
          <div className="mt-[-80px] lg:pt-6 lg:z-20 lg:relative lg:bg-white/95 backdrop-blur-md rounded-t-3xl mx-4 shadow-2xl border border-gray-200/50 px-6">
            <div className="pt-2">
              <ExploreFilter allPackages={packageList} />
            </div>

            {/* Package Grid */}
            {filteredPackages?.length > 0 && (
              <div className="mt-8 grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4 gap-6 pb-8">
                {filteredPackages.map((pkg, index) => (
                  <div
                    key={index}
                    onClick={() => handleNextPage(pkg.packageId)}
                    className="cursor-pointer group"
                    ref={
                      index === packageList.length - 1 ? lastElementRef : null
                    }
                  >
                    <FilterCardList package={pkg} />
                  </div>
                ))}
              </div>
            )}

            {!isLoading && filteredPackages?.length < 1 && (
              <div className="flex flex-col items-center justify-center py-24 text-center">
                <div className="w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center mb-6">
                  <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400">
                    <path d="M21.21 15.89A10 10 0 1 1 8 2.83"/>
                    <path d="M22 12A10 10 0 0 0 12 2v10z"/>
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-2">No Packages Found</h3>
                <p className="text-gray-600 max-w-md leading-relaxed">We couldn't find any packages matching your criteria. Please try adjusting your filters or search terms.</p>
                <button
                  onClick={() => window.location.reload()}
                  className="mt-6 px-6 py-3 bg-gradient-to-r from-[#FF5F5F] to-[#FF7865] text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-105"
                >
                  Reset Filters
                </button>
              </div>
            )}

            {isLoading && <PackagesLoading />}
          </div>
        </div>
        <div className="lg:hidden">
          <div className="lg:flex lg:gap-4 mt-6 lg:px-3">
            <div className="lg:flex-1 lg:w-1/2">
              <div className=" mt-6 px-3  sm:block">
                <SwiperComponent />
              </div>
              <ExploreFilter allPackages={packageList} />
            </div>
            <div className="px-1 sm:flex-none ">
              <div className="sm:hidden lg:block mt-6 flex flex-col justify-center pl-3">
                <header className=" text-[#1EC089] text-left  font-semibold sm:text-[20px] lg:text-[24px]  ">
                  List of Packages
                </header>
                <h1 className="text-neutral-600 sm:text-xs lg:text-sm sm:mt-0 lg:mt-1 ">
                  Various Packages available for{" "}
                  <span className=" text-neutral-600 font-medium text-sm">
                    {destination}
                  </span>
                </h1>
              </div>
              {filteredPackages?.length > 0 && (
                <div className="mt-4">
                  {filteredPackages.map((pkg, index) => (
                    <div
                      onClick={() => handleNextPage(pkg.packageId)}
                      className="cursor-pointer transition-colors"
                      key={index}
                    >
                      <div
                        className="mx-2 mb-4"
                        ref={
                          index === packageList.length - 1
                            ? lastElementRef
                            : null
                        }
                      >
                        <FilterCardList package={pkg} />
                      </div>
                    </div>
                  ))}
                </div>
              )}
              {!isLoading && filteredPackages?.length < 1 && (
                <div className="flex flex-col items-center justify-center py-20 text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400 mb-4"><path d="M21.21 15.89A10 10 0 1 1 8 2.83"/><path d="M22 12A10 10 0 0 0 12 2v10z"/></svg>
                  <h3 className="text-xl font-semibold text-gray-700">No Packages Found</h3>
                  <p className="text-gray-500 mt-2">We couldn't find any packages matching your criteria. <br /> Please try adjusting your filters.</p>
                </div>
              )}

              {isLoading && <PackagesLoading />}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Page;
